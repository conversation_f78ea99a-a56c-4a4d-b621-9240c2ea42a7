<?php
// Redirect from mobile
$IsMobile = isset($_GET['IsMobile']) ? ($_GET['IsMobile']) : 0;

include('../class/clsExternalPreceptors.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');

$preceptorId = 0;
$externalPreceptorId = 0;

$preceptorNum = isset($_GET['preceptorNum']) ? DecodeQueryData($_GET['preceptorNum']) : '';
$isCompletion = isset($_GET['isCompletion']) ? DecodeQueryData($_GET['isCompletion']) : 0;
$externalPreceptorId = isset($_GET['preceptorId']) ? DecodeQueryData($_GET['preceptorId']) : 0;
$isRegister = isset($_GET['isRegister']) ? ($_GET['isRegister']) : 0;

$externalPreceptorFirstName = '';
$standardPreceptors = '';
$objExternalPreceptors = new clsExternalPreceptors();

if ($preceptorNum) {
    $isPreceptorExist = $objExternalPreceptors->GetExternalPreceptorDetail($externalPreceptorId, $preceptorNum);
    $externalPreceptorFirstName = isset($isPreceptorExist['firstName']) ? $isPreceptorExist['firstName'] : '';
    $externalPreceptorLastName = isset($isPreceptorExist['lastName']) ? $isPreceptorExist['lastName'] : '';
    $externalPreceptorEmail = isset($isPreceptorExist['email']) ? $isPreceptorExist['email'] : '';
    $externalPreceptorId = isset($isPreceptorExist['id']) ? $isPreceptorExist['id'] : '';
    $standardPreceptors = serialize($preceptorNum);
}


if (!$preceptorNum)
    include('includes/validateUserLogin.php');


include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../setRequest.php');
include('../class/clsschoolclinicalsiteunit.php');
include('../class/clsStudentMinCharacterEntry.php');
include('../class/clsClinician.php');
include('../class/clsActivitysheet.php');
include('../class/clsProcedureCount.php');
include('../class/clsPreceptorDetails.php');


$page_title = "Add Activity Sheet";
$bedCrumTitle = 'Add';

$view = '';
$rotationId = 0;
$physicianId = 0;
$procedureCategoryId = 0;
$studentId = 0;

$hospitalSiteUnitId = 0;
$drhospitalSiteUnitId = 0;
$journalCount = 0;
$interactionCount = 0;
$journalStudentEntry = '';
$locationId  = 0;
$isActiveCheckoff = '';
$selectedInteraction = '';

$schoolId = $currentSchoolId;
$transchooldisplayName = $currenschoolDisplayname;
$TimeZone = isset($_SESSION["loggedStudentSchoolTimeZone"]) ? $_SESSION["loggedStudentSchoolTimeZone"] : '';
$studentId = isset($_SESSION["loggedStudentId"]) ? $_SESSION["loggedStudentId"] : 0;
$isActiveCheckoff = $isActiveCheckoffForStudent;

$journalId = 0;
$activityId = 0;
$clinicianId = 0;
$title = "Procedure Count ";
$timeSpent = '';
$pointsAwarded = '';
$interactionSummary = '';
$hospitalSiteInteractionUnitId = '';
$activityDate = '';
$isSendToExternalPreceptor = isset($_GET['isSendToExternalPreceptor']) ? $_GET['isSendToExternalPreceptor'] : 0;
$isSendToPreceptor = 0;
$clinicianComment = '';
$schoolComment = '';
$precptorName = '';
$procedurecountId = '';
$objActivity = new clsActivitysheet();

$rotationId = isset($_GET['rotationId']) ? DecodeQueryData($_GET['rotationId']) : 0;

//Get Active Rotations
$objRotation = new clsRotation();
$rotation = $objRotation->GetAllActiveRotationForDropdown($schoolId, $studentId);

//Get Hospital Site Unit 
$objhospitalsiteunit = new clsschoolclinicalsiteunit;
$hospitalsiteunit = $objhospitalsiteunit->GetAllClinicalSiteUnit($currentSchoolId);
unset($objhospitalsiteunit);

//Get Physician Interaction
$physician = $objActivity->GetAllPhysicians();

//For Journal Character Count 
$objMinChar = new clsStudentMinCharacterEntry();
$rowCount = $objMinChar->GetStudentJournalEntryCount($currentSchoolId);
$journalCount = $rowCount['journalCharacters'];
$journalCount = isset($journalCount) ? $journalCount : 0;

//interaction count
$interactionCount = $rowCount['interactionCharacterCount'];
$interactionCount = isset($interactionCount) ? $interactionCount : 0;

$status = isset($_GET["status"]) ? $_GET["status"] : '';

//For Edit
if (isset($_GET['editactivityid'])) {

    $page_title = "Edit Activity Sheet";
    $bedCrumTitle = 'Edit';

    $activityId = DecodeQueryData($_GET['editactivityid']);

    $row = $objActivity->GetAllActivitySheetDetails($activityId, $schoolId);

    $objDB = new clsDB();
    $retPreceptorId = $objDB->GetSingleColumnValueFromTable('preceptordetails', 'preceptorId', 'referenceId', $activityId, 'type', 'activitySheet');
    unset($objDB);

    if ($preceptorNum) {
        $isPreceptorCompletedStatus  = stripslashes($row['isPreceptorCompletedStatus']);
        if ($isPreceptorCompletedStatus) {
            header('location:thankyou.html?isType=activitySheet');
            exit();
        }
    }
    if ($externalPreceptorId > 0 && $retPreceptorId !== $externalPreceptorId) {
        header('location:thankyou.html?status=Invalid');
        exit;
    }

    //Get Time Zone By Rotation 
    $objLocation = new clsLocations();
    $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
    unset($objLocation);
    if ($TimeZone == '')
        $TimeZone = isset($_SESSION["loggedStudentSchoolTimeZone"]) ? $_SESSION["loggedStudentSchoolTimeZone"] : SERVER_TIMEZONE;


    $physicianId = $row['physicianinteractionId'];
    $rotationId = $row['rotationId'];
    $activityDate = $row['activityDate'];
    if ($activityDate != '0000-00-00' && $activityDate != '' && $activityDate != '0000-00-00 00:00:00' && $activityDate != '01/01/1970') {
        // echo $activityDate = converFromServerTimeZone($activityDate, $TimeZone);
        $activityDate = date("m/d/Y", strtotime($activityDate));
    } else {
        $activityDate = '';
    }
    $hospitalSiteUnitId = $row['journalHospitalSiteUnitId'];
    $hospitalSiteInteractionUnitId = $row['interactionHospitalSiteUnitId'];
    $journalStudentEntry = $row['journalStudentEntry'];
    $timeSpent = $row['timeSpent'];
    $pointsAwarded = $row['pointsAwarded'];
    $interactionSummary = $row['interactionSummary'];
    $isSendToPreceptor = $row['isSendToPreceptor'];
    $journalId = $row['journalId'];
    $interactionId = $row['interactionId'];
    $procedurecountId = $row['procedurecountId'];
    $clinicianId = $row['clinicianId'];
    $clinicianComment  = stripslashes($row['clinicianComment']);
    $schoolComment  = stripslashes($row['schoolComment']);
    $selectedInteraction  = stripslashes($row['selectedInteraction']);


    $clinicianSignatureDate = $row['clinicianSignatureDate'];
    if ($clinicianSignatureDate != '0000-00-00' && $clinicianSignatureDate != '' && $clinicianSignatureDate != '0000-00-00 00:00:00' && $clinicianSignatureDate != '01/01/1970') {
        // $clinicianSignatureDate = converFromServerTimeZone($clinicianSignatureDate, $TimeZone);
        $clinicianSignatureDate = date("m/d/Y", strtotime($clinicianSignatureDate));
    } else {
        $clinicianSignatureDate = '';
    }

    $schoolSignatureDate = $row['schoolSignatureDate'];
    if ($schoolSignatureDate != '0000-00-00' && $schoolSignatureDate != '' && $schoolSignatureDate != '0000-00-00 00:00:00' && $schoolSignatureDate != '01/01/1970') {
        // $schoolSignatureDate = converFromServerTimeZone($schoolSignatureDate, $TimeZone);
        $schoolSignatureDate = date("m/d/Y", strtotime($schoolSignatureDate));
    } else {
        $schoolSignatureDate = '';
    }

    // Get preceptor details
    $objPreceptorDetails = new clsPreceptorDetails();
    $preceptorDetails = $objPreceptorDetails->GetActivitySheetPreceptorDetails($activityId);
    if ($preceptorDetails != '') {

        $preceptorId = $preceptorDetails['preceptorId'];
        $status = $preceptorDetails['status'];
        $signatureDate = $preceptorDetails['signatureDate'];
        $mobile_num = $preceptorDetails['mobile_num'];
        $firstName = $preceptorDetails['firstName'];
        $lastName = $preceptorDetails['lastName'];
        $precptorName = ($firstName != '' && $lastName != '') ? $firstName . ' ' . $lastName : '';
        $clinicianDate = $signatureDate;
    }
}

if (isset($_GET['view'])) {
    $view = $_GET['view'];
    $bedCrumTitle = 'View';
}
$view = ($view == 'V') ? 1 : 0;

?>


<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content
        must come *after* these tags -->
    <title>
        <?php echo ($page_title); ?>
    </title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/toggleButton.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/stepper.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <script type="module" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"></script>
    <!-- <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/css/style.css"> -->

    <style>
        .d-flex {
            display: flex;
        }

        .margin-right {
            margin-right: 7px;
        }

        .checkboxStyle {
            margin: 0 !important;
            width: 50%;
            border: 1px solid #ccc;
        }

        .alignCenter {
            align-items: center !important;
        }

        .abnormal {
            color: red !important;
        }

        .btn-container {
            display: flex;
            align-items: center;
            width: fit-content;
        }

        .btn-color-mode-switch {
            display: flex;
            align-items: center;
            width: fit-content;
            margin-left: 7px;
        }

        .panel {
            border: 2px solid #ddd;
        }

        .precedure-count-card {
            padding: 15px;
            border-radius: 15px;
            border: 2px solid #ddd;
            /* box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px 0px, rgba(0, 0, 0, 0.1) 0px 0px 1px 0px; */
        }

        .procedure-count-card-header {
            display: flex;
            justify-content: space-between;
        }

        .procedure-count-name {
            font-size: 15px;
            font-weight: 600;
        }

        .mb-0 {
            margin-bottom: 0;
        }

        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #01A750; */
            /* color: #fff; */
            color: #555;
            background: #f6f9f9;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }


        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 45px !important;
        }

        .required-select2 {
            border-left: solid 3px red !important;
            border-radius: 12px !important;
        }

        .select2-container--default .select2-selection--single {
            background-color: #f6f9f9 !important;
            cursor: default !important;
            height: 45px !important;
            border-radius: 10px !important;
        }

        .select2-container--default .select2-selection--single {
            border: none !important;
        }

        .panel,
        .form-group {
            margin-bottom: 10px;
        }

        .bootstrap-datetimepicker-widget {
            border-radius: 12px !important;
        }

        .form-control {
            height: 45px;
        }

        textarea.form-control {
            height: auto;
        }

        .pl-0 {
            padding-left: 0;
        }

        .blood-pressure-input {
            width: 80px;
            display: inline-block;
            margin: 5px 10px;
        }

        .abnormal-value-checkbox {
            padding: 0;
            width: 37px;
            height: 37px;
            margin-left: 10px;
        }

        .row-delete-icon {
            font-size: 18px;
            padding-left: 6px;
        }

        .mobile-block {
            display: flex;
            align-items: center;
        }

        /* .form-horizontal .form-group {
            margin-right: 0;
            margin-left: 0;
        } */


        .select2-container {
            width: 100% !important;
        }

        ion-icon {
            pointer-events: none;
        }

        .icon-inner,
        .ionicon,
        svg {
            color: #01A750;
        }

        .form-stepper-horizontal li:not(:last-child):after {
            height: 2px;
            top: 33%;
        }

        .button {
            padding: 10px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 500;
        }

        ul.form-stepper li a .form-stepper-circle {
            width: 35px;
            height: 35px;
            line-height: 1.7rem;
            margin-top: -5px;
            font-size: 20px;
            font-weight: 600;
            box-shadow: rgba(0, 0, 0, 0.15) 2.4px 2.4px 3.2px;
        }

        .stepper-label {
            font-size: 15px !important;
        }

        .form-stepper .label {
            padding: 0.8em .6em .3em;
            line-height: 1;
        }

        .arrow-icon {
            transition: transform 0.3s;
        }

        .collapsible.expanded .arrow-icon i {
            transform: rotate(180deg);
        }

        .collapsible-arrow.rotate {
            transform: rotate(180deg);
        }

        .panel-collapse.collapse.active {
            display: block;
            max-height: fit-content !important;
            transition: max-height 0.3s ease;
        }

        .panel-heading {
            padding: 5px;
        }

        .panel-body {
            overflow-x: auto;
            padding-top: 5px;
        }

        .position-relative {
            position: relative;
        }

        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .bootstrap-datetimepicker-widget {
            z-index: 9999;
        }

        .procedeure-count-form-field-main {
            display: flex;
        }

        .procedure-count-form-field {
            width: 50%;
            padding-right: 15px;
            padding-left: 15px;
        }

        .text-right {
            text-align: right;
        }

        /* new style start */

        .hidden-cards {
            display: none;
        }

        .hr-margin {
            margin-top: 5px !important;
            margin-bottom: 5px !important;
            /* margin-left: -15px; */
        }

        .getProcedureSteps {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
        }

        .mobile-mb-0 {
            margin-bottom: 10px;
        }

        .mobile-mt-0 {
            margin-top: 20px;
        }

        .procedure-name-section {
            max-width: 75%;
        }

        /* new style end */

        @media screen and (max-width: 768px) {
            .panel-body {
                padding: 0 5px;
            }

            /* .card-mobile-padding {
                padding: 0;
            } */

            .mobile-mb-0 {
                margin-bottom: 0px;
            }

            .mobile-mt-0 {
                margin-top: 0px;
            }

        }


        @media screen and (max-width: 500px) {
            .panel-body ol {
                padding-left: 20px;
            }

            .panel-default>.panel-heading {
                padding-left: 5px;
            }

            .blood-pressure-input {
                width: 100%;
                margin: 5px 0;
            }

            /* .panel-body {
                padding: 0 5px;
            } */

            .panel-body {
                padding: 0 15px;
            }

            .btn-color-mode-switch {
                margin-left: 0px;
            }

            .card-mobile-padding {
                /* padding: 4px; */
                padding: 0;
            }

            /* .procedure-count-form-field {
                padding-right: 7px;
                padding-left: 7px;
            } */

            .btn-container {
                width: fit-content;
                flex-direction: column;
                align-items: self-start;
                gap: 5px;
            }

            .mobile-margin {
                margin-left: 0;
                margin-right: 0;
            }

            .panel-group .panel {
                padding: 10px;
            }

            /* .container-zero{
            padding: 0;
         } */

            .mobile-gray-border {
                border: 1px solid #ececec;
            }

            .panel-group .panel-heading {
                width: 100%;
            }

            .collapsible {
                cursor: pointer;
                width: 100%;
                /* padding: 15px; */
                /* border: 1px solid #181818; */
                /* background-color: #f9f9f9; */
                display: flex;
                justify-content: space-between;
                align-items: center;
                /* border-radius: 14px; */
            }

            .collapsible p {
                margin: 0;
            }

            .collapsible-arrow {
                font-size: 18px;
                transition: transform 0.3s ease;
            }

            .content {
                display: none;
                padding: 10px 0;
                /* border-top: 1px solid #ececec; */
            }

            .content.active {
                display: block;
            }

            .active.collapsible-arrow {
                transform: rotate(180deg);
            }

            .row-delete-icon {
                position: absolute;
                top: -82px;
                right: 20px;
            }

            .mobile-block {
                display: block;
            }

            /* .form-stepper-horizontal li {
                min-width: 90px;
            } */

            .form-step {
                padding: 2rem 1rem;
            }

            .pt-0 {
                padding-top: 0 !important;
            }

            .mobile-mb-0 {
                margin-bottom: 0px;
            }

            .mobile-mt-0 {
                margin-top: 0px;
            }

            .stepper-label {
                font-size: 14px !important;
            }

            #step-1,
            #step-2,
            #step-4 {
                padding-left: 0;
                padding-right: 0;
            }
        }
    </style>
</head>

<body>
<a href="registerExternalPreceptor.html?externalPreceptorId=<?php echo $externalPreceptorId; ?>&isHours=false" id="registerPage" class="addCommentpopup hide" data-organizationid="">Add</a>
    
    <!-- <div id="loading-div-background">
        <div id="loading-div" class="ui-corner-all">
            <img style="height:31px;width:31px;margin:30px;" src="<?php echo ($dynamicOrgUrl); ?>/assets/images/loader.gif" alt="Loading.." /><br>PROCESSING. PLEASE WAIT...
        </div>
    </div> -->
    <?php if (!$preceptorNum && $IsMobile == 0) { ?>

        <?php include('includes/header.php'); ?>
        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="activitysheetlist.html">Activity Sheet</a></li>
                        <li class="active">
                            <?php echo ($bedCrumTitle); ?>
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    <?php  } else  ?>
    <!-- <br> -->
    <div class="container mb-15 mobile-padding-4">
        <?php if($isAutoSave && $activityId == 0) { ?>
        <!-- Auto Save Floating Button -->
        <?php include('../includes/autosaveBtn.php'); ?>
        <!--Auto Save Floating Buttons End-->        
        <?php } ?> 

        <form id="formActivitySheet" autocomplete="off" data-parsley-validate="data-parsley-validate" class="form-horizontal" method="POST" <?php if (isset($_GET['isSendToExternalPreceptor'])) { ?> action="activitysheetsubmit.html?editactivityid=<?php echo EncodeQueryData($activityId); ?>&isPreceptor=1" <?php } elseif ($activityId) { ?> action="activitysheetsubmit.html?editactivityid=<?php echo EncodeQueryData($activityId); ?>" <?php } else { ?> action="activitysheetsubmit.html" <?php } ?>>

            <!-- Mobile redirect -->
            <input type="hidden" name="IsMobile" id="IsMobile" value="<?php echo $IsMobile; ?>">
            <input type="hidden" name="currentSchoolId" id="currentSchoolId" value="<?php echo $currentSchoolId; ?>">
            <input type="hidden" name="hiddenStudentId" id="userId" value="<?php echo $studentId; ?>">
            <input type="text" name="draftId" id="hiddendraftId" value="">
            <input type="hidden" name="moduleName" id="moduleName" value="activitysheet">

            <div class="row mobile-margin mobile-mb-0">

                <div class="col-md-6">

                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cborotation">Rotation</label>
                        <div class="col-md-12">
                            <select id="cborotation" name="cborotation" class="form-control input-md  select2_single required-input" data-parsley-errors-container="#error-cborotation" required>
                                <option value="" selected>Select</option>
                                <?php
                                if ($rotation != "") {
                                    while ($row = mysqli_fetch_assoc($rotation)) {
                                        $selrotationId  = $row['rotationId'];
                                        $name  = stripslashes($row['title']);

                                ?>
                                        <option value="<?php echo ($selrotationId); ?>" <?php if ($rotationId == $selrotationId) { ?> selected="true" <?php } ?>>
                                            <?php echo ($name); ?>
                                        </option>

                                <?php

                                    }
                                }
                                ?>
                            </select>
                            <div id="error-cborotation"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mobile-mt-0">
                    <div class="form-group">
                        <label class="col-md-12 control-label mobile-hide" style="visibility: hidden;" for=""> </label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class="btn-container">
                                <label>Activity Sheet Send To</label>
                                <label class="switch btn-color-mode-switch">
                                    <!-- &nbsp;&nbsp; -->
                                    <input type="checkbox" name="isSendToPreceptor" id="mode" value="1">
                                    <label for="mode" data-on="Preceptor" data-off="Clinician" class="btn-color-mode-switch-inner"></label>

                                </label>

                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <div class="row mobile-margin mobile-mb-0">

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="activitysheetdate"> Date</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class='input-group date w-full' id='activitysheetdate1' style="position: relative;">
                                <input type='text' name="activitysheetdate" id="activitysheetdate" placeholder="MM-DD-YYYY" class="form-control input-md required-input rotation_date" value="<?php echo $activityDate; ?>" data-parsley-errors-container="#error-txtDate" required />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-txtDate"></div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <?php
                    if ($activityId && $preceptorId) {
                    ?>
                        <div id="preceptorDetails" class="form-group">
                            <label class="col-md-12 control-label" for="">Preceptor</label>
                            <div class="col-md-12">
                                <input id="preceptorName" name="preceptorName" type="text" placeholder="" value="<?php echo $precptorName; ?>" class="form-control input-md">
                            </div>
                        </div>
                    <?php  } else { ?>
                        <div id="preceptor-mobile-number" class="form-group" style="display: none;">
                            <label class="col-md-12 control-label" for="">Preceptor Mobile Number</label>
                            <div class="col-md-12">
                                <input id="preceptorNo" maxlength="12" data-inputmask-alias="************" name="preceptorNo" maxlength="12" type="text" placeholder="___-___-____" class="form-control input-md required-input">
                            </div>
                            <span id="txtpreceptorNo" style="color: red; margin-left: 25px;"> </span>
                        </div>
                    <?php } ?>
                    <?php if (!$preceptorId) { ?>

                        <div class="form-group" id="clinician">
                            <label class="col-md-12 control-label" for="cboClinicalinstructor">Clinical Instructor</label>
                            <div class="col-md-12">
                                <select id="cboClinicalinstructor" name="cboClinicalinstructor" class="form-control select2_single required-input " data-parsley-errors-container="#error-cboClinicalinstructor">
                                    <option value="" selected>Select</option>
                                    <option value=""> </option>
                                </select>
                                <div id="error-cboClinicalinstructor"></div>
                            </div>
                        </div>
                    <?php } ?>
                </div>

            </div>
            <hr>
            <!-- stepper start -->
            <div style="margin-top: 30px;">
                <div id="multi-step-form-container">
                    <!-- Form Steps / Progress Bar -->
                    <ul class="form-stepper form-stepper-horizontal text-center mx-auto">
                        <!-- Step 1 -->
                        <li class="form-stepper-active text-center form-stepper-list" step="1">
                            <a class="mx-2">
                                <span class="form-stepper-circle">
                                    <span>1</span>
                                </span>
                                <div class="label stepper-label">Journal</div>
                            </a>
                        </li>
                        <!-- Step 2 -->
                        <li class="form-stepper-unfinished text-center form-stepper-list" step="2">
                            <a class="mx-2">
                                <span class="form-stepper-circle text-muted">
                                    <span>2</span>
                                </span>
                                <div class="label text-muted stepper-label">Dr.Interaction</div>
                            </a>
                        </li>
                        <!-- Step 3 -->
                        <li class="form-stepper-unfinished text-center form-stepper-list" step="3">
                            <a class="mx-2">
                                <span class="form-stepper-circle text-muted">
                                    <span>3</span>
                                </span>
                                <div class="label text-muted stepper-label">Procedure Count</div>
                            </a>
                        </li>
                        <!-- Step 4 -->
                        <li class="form-stepper-unfinished text-center form-stepper-list" step="4">
                            <a class="mx-2">
                                <span class="form-stepper-circle text-muted">
                                    <span>4</span>
                                </span>
                                <div class="label text-muted stepper-label">Comments</div>
                            </a>
                        </li>

                    </ul>
                </div>
            </div>

            <section id="step-1" class="form-step pt-0" data-parsley-validate>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="Hospital Site">Hospital Site</label>
                            <div class="col-md-12">
                                <input type="text" class="form-control input-md" value="" name="HospitalSite" id="HospitalSite" readonly>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">

                        <div class="form-group">
                            <label class="col-md-12 control-label" for="cbohospitalsiteunits">Hospital Site Unit</label>
                            <div class="col-md-12">
                                <select id="cbohospitalsiteunits" name="cbohospitalsiteunits" class="form-control input-md required-input select2_single" data-parsley-errors-container="#error-cbohospitalsiteunits" placeholder="Select Hospital site Unit" required>
                                    <option value="" selected>Select</option>
                                    <?php
                                    if ($hospitalsiteunit != "") {
                                        $hospitalsiteunit->data_seek(0); // Reset pointer to the start
                                        while ($row = mysqli_fetch_assoc($hospitalsiteunit)) {
                                            $selhospitalSiteUnitId  = $row['schoolClinicalSiteUnitId'];
                                            $name  = stripslashes($row['title']);

                                    ?>
                                            <option value="<?php echo ($selhospitalSiteUnitId); ?>" <?php if ($hospitalSiteUnitId == $selhospitalSiteUnitId) { ?> selected="true" <?php } ?>>
                                                <?php echo ($name); ?>
                                            </option>

                                    <?php

                                        }
                                    }
                                    ?>
                                </select>

                                <div id="error-cbohospitalsiteunits"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="Student Journal Entry">Student Journal Entry</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <textarea name="student_journal_entry" id="student_journal_entry" class="form-control input-md mb-10" rows="4" cols="100"><?php echo $journalStudentEntry;
                                                                                                                                                            ?></textarea>
                                <div id="studentJournal-count" class="character-count" style="float: right;">
                                    <span id="studentJournal-current">0</span>
                                    <span id="studentJournal-maximum">/ <?php echo $journalCount;
                                                                        ?></span>
                                </div>

                                <div id="entryError"></div>

                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-3 flex-end">
                    <button class="button next btn-navigate-form-step " id="btnJournal" type="button" step_number="2">Next</button>
                </div>
            </section>
            <!-- Step 2 Content, default hidden on page load. -->
            <section id="step-2" class="form-step d-none pt-0" data-parsley-validate>
                <div class="row">

                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="cbodrhospitalsiteunits">Hospital Site Unit</label>
                            <div class="col-md-12">
                                <select id="cbodrhospitalsiteunits" name="cbodrhospitalsiteunits" class="form-control input-md required-input select2_single" placeholder="Select Hospital site Unit" required data-parsley-errors-container="#error-cbodrhospitalsiteunits">
                                    <option value="" selected>Select</option>
                                    <?php
                                    if ($hospitalsiteunit != "") {
                                        $hospitalsiteunit->data_seek(0); // Reset pointer to the start

                                        while ($row = mysqli_fetch_assoc($hospitalsiteunit)) {
                                            $selhospitalSiteUnitId  = $row['schoolClinicalSiteUnitId'];
                                            $name  = stripslashes($row['title']);

                                    ?>
                                            <option value="<?php echo ($selhospitalSiteUnitId); ?>" <?php if ($hospitalSiteInteractionUnitId == $selhospitalSiteUnitId) { ?> selected="true" <?php } ?>>
                                                <?php echo ($name); ?>
                                            </option>

                                    <?php

                                        }
                                    }
                                    ?>
                                </select>
                                <div id="error-cbodrhospitalsiteunits"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="cbophysician">Physician Interaction</label>
                            <div class="col-md-12">
                                <select id="cbophysician" name="cbophysician" class="form-control input-md  select2_single">
                                    <option value="" selected>Select</option>
                                    <?php
                                    if ($physician != "") {
                                        while ($row = mysqli_fetch_assoc($physician)) {
                                            $selphysicianId  = $row['physicianinteractionId'];
                                            $name  = stripslashes($row['physicianinteractionTitle']);

                                    ?>
                                            <option value="<?php echo ($selphysicianId); ?>" <?php if ($physicianId == $selphysicianId) { ?> selected="true" <?php } ?>>
                                                <?php echo ($name); ?>
                                            </option>

                                    <?php

                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">

                        <!-- Text input-->
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="TimeSpent">Amount of Time Spent</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <div class='input-group date w-full'>

                                    <input type='text' name="txtTimeSpent" id="txtTimeSpent" class="form-control input-md" maxlength="3" pattern="/^[0-9]*$/" value="<?php echo $timeSpent; ?>" data-parsley-type="digits" data-parsley-errors-container="#error-digits" />

                                </div>

                            </div>
                            <span class="small-info col-md-12 col-sm-12 col-xs-12">e.g.If Minutes, i.e. 01 equals 1 minute</span>
                        </div>

                    </div>
                    <div class="col-md-6">

                        <!-- Text input-->
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="txtPointsAwarded">Points Awarded</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <div class='input-group date w-full mb-15'>
                                    <input type='text' data-parsley-validation-threshold="1" data-parsley-trigger="keyup" data-parsley-type="digits" name="txtPointsAwarded" id="txtPointsAwarded" class="form-control input-md" value="<?php echo ($pointsAwarded); ?>" <?php if ($isActiveCheckoff != 2) echo 'disabled'; ?> />
                                </div>
                            </div>
                            <?php if ($isActiveCheckoff == 2) { ?>
                                <div class="col-md-12" style="margin-top: 10px;">
                                    <select id="interactionType" name="interactionType" class="form-control select2_single">
                                        <option>Select</option>
                                        <option value="Physician Interaction -4" <?php if ($selectedInteraction == 'Physician Interaction -4') { ?> selected="true" <?php } ?>>Physician Interaction – 4 points</option>
                                        <option value="RN Interaction -2" <?php if ($selectedInteraction == 'RN Interaction -2') { ?> selected="true" <?php } ?>>RN Interaction – 2 points</option>
                                        <option value="Shift Change -1" <?php if ($selectedInteraction == 'Shift Change -1') { ?> selected="true" <?php } ?>>Shift Change – 1 point</option>
                                    </select>
                                </div>
                            <?php } ?>

                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="Student Journal Entry">Student Response</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <textarea name="Student_Response" id="Student_Response" class="col-md-12 form-control input-md mb-10" rows="4" cols="100"><?php echo $interactionSummary;
                                                                                                                                                            ?></textarea>

                                <div id="Student_Response-count" class="character-count" style="float: right;">
                                    <span id="Student_Response-current">0</span>
                                    <span id="Student_Response-maximum">/ <?php echo $interactionCount;
                                                                            ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3 flex-end">
                    <button class="button btn-navigate-form-step button-prev" type="button" step_number="1">Prev</button>
                    <button class="button next btn-navigate-form-step" id="btnInteraction" type="button" step_number="3">Next</button>
                </div>
            </section>
            <!-- Step 3 Content, default hidden on page load. -->
            <section id="step-3" class="form-step d-none pt-0" data-parsley-validate>

                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">

                            <div class="col-md-12 col-sm-12 col-xs-12 card-mobile-padding">
                                <div class="panel-group" id="posts">
                                    <?php

                                    include('../class/clsProcedureCategory.php');
                                    include('../class/clsUsafProcedureCategory.php');
                                    include('../class/clscheckoff.php');

                                    //For Procedure Category Name
                                    $objProcedureCategory = new clsProcedureCategory();
                                    $objUsafProcedureCategory = new clsUsafProcedureCategory();
                                    $objcheckoff = new clscheckoff();
                                    $jsonProcedureCountsCodeArray = 0;
                                    $procedureCountsCodeArray = [];
                                    $topicarray = [];
                                    $usafSchoolId = ($schoolId == 122) ? 122 : 0;

                                    if ($isActiveCheckoff == 1)
                                        $Category = $objProcedureCategory->GetAllCategory();
                                    elseif ($isActiveCheckoff == 2)
                                        $Category = $objUsafProcedureCategory->GetAllUsafCategory($usafSchoolId);
                                    else
                                        $Category = $objProcedureCategory->GetAllAdvanceCategory();

                                    //For Procedure 
                                    $ProcedureCategory = $objcheckoff->GetProcedureCategory($currentSchoolId);
                                    if ($ProcedureCategory != '') {
                                        $totalSection = mysqli_num_rows($ProcedureCategory);
                                    }
                                    while ($row = mysqli_fetch_array($Category)) {
                                        $procedureCategoryId = $row['procedureCategoryId'];
                                        $title = $row['categoryName'];
                                        $objProcedureCount = new clsProcedureCount();
                                        $categorywiseProcedureCount = 0;
                                        if ($procedurecountId != '') {
                                            if ($isActiveCheckoff == 1) {
                                                $categorywiseProcedureCount = $objProcedureCount->GetStandardCategorywiseProcedureCount($procedureCategoryId, $procedurecountId);
                                            } elseif ($isActiveCheckoff == 2) {
                                                $categorywiseProcedureCount = $objProcedureCount->GetUsafCategorywiseProcedureCount($procedureCategoryId, $procedurecountId);
                                            } else {
                                                $categorywiseProcedureCount = $objProcedureCount->GetAdvanceCategorywiseProcedureCount($procedureCategoryId, $procedurecountId);
                                            }
                                        }
                                        unset($objProcedureCount);
                                    ?>
                                        <div class="panel panel-default">
                                            <a class="collapsible" style="color: #000; text-decoration: none;" href="javascript:void(0);" data-toggle="collapse" data-parent="#posts" id="collapse-link">
                                                <div class="panel-heading" style="display: flex; justify-content: space-between; align-items: center;">
                                                    <h4 class="panel-title" <?php if ($categorywiseProcedureCount > 0) { ?> style="color: #01A750 !important;" <?php } ?>>
                                                        <?php echo  $title; ?>
                                                    </h4>
                                                    <span class="arrow-icon"> <i class="fa fa-angle-down" aria-hidden="true" id="collapse-icon" style="font-size: 19px;"></i></span>
                                                </div>
                                            </a>

                                            <div id="<?php echo $procedureCategoryId; ?>" class="panel-collapse collapse">
                                                <div class="panel-body" id="data_id_<?php echo $procedureCategoryId; ?>">
                                                    <hr class="hr-margin">
                                                    <div id="card-container">
                                                        <div class="row">
                                                            <!-- <table id="activitysheet" class="datatable-responsive mytablecustom <?php echo $procedureCategoryId; ?> calculateTableValues table table-bordered dt-responsive nowrap table-hover" cellspacing="0" style="width:100%">
                                                        <thead>
                                                            <tr>
                                                                <th style="text-align: center;">ID</th>
                                                                <th style="text-align: center;">Procedure Name</th>
                                                                <th style="text-align: center;">Asstd</th>
                                                                <th style="text-align: center;">Obsvd</th>
                                                                <th style="text-align: center;">Prfmd</th>
                                                                <th style="text-align: center;">Prfmd Total</th>
                                                                <th style="text-align: center;">Procdr Total</th>
                                                                <th style="text-align: center;">Procedure Date</th>
                                                                <th style="text-align: center;">Evaluation Date</th>
                                                            </tr>

                                                        </thead>

                                                        <tbody> -->

                                                            <?php

                                                            $procedurecountIds = '';

                                                            $objDB = new clsDB();
                                                            $courseId = $objDB->GetSingleColumnValueFromTable('rotation', 'courseId', 'rotationId', $rotationId);
                                                            $checkoffType = $objDB->GetSingleColumnValueFromTable('schools', 'checkoffType', 'schoolId', $currentSchoolId);
                                                            $procedureStatus = $objDB->GetSingleColumnValueFromTable('schoolsettings', 'status', 'schoolId', $currentSchoolId, 'type', 'procedureCount');

                                                            unset($objDB);
                                                            if ($externalPreceptorId)
                                                                $procedurecountIds = ($procedurecountId) ? $procedurecountId : 0;


                                                            $totalProcedureCount = 0;
                                                            if ($isActiveCheckoff == 1) {
                                                                if (!$externalPreceptorId) {
                                                                    if ($procedureStatus == 1)
                                                                        $ProcedureCount = $objProcedureCategory->GetAllProcedureCountByCourse($procedureCategoryId, $currentSchoolId, $courseId, $checkoffType);
                                                                    else
                                                                        $ProcedureCount = $objProcedureCategory->GetAllProcedureCountByCategory($procedureCategoryId, $currentSchoolId, $checkoffType);
                                                                } else {
                                                                    if ($procedureStatus == 1)
                                                                        $ProcedureCount = $objProcedureCategory->GetAllProcedureCountForPreceptor($procedureCategoryId, $currentSchoolId, $courseId, $procedurecountIds, $isActiveCheckoff, $checkoffType);
                                                                    else
                                                                        $ProcedureCount = $objProcedureCategory->GetAllProcedureCountForPreceptor($procedureCategoryId, $currentSchoolId, 0, $procedurecountIds, $isActiveCheckoff, $checkoffType);
                                                                }
                                                            } elseif ($isActiveCheckoff == 2) {
                                                                if (!$externalPreceptorId) {
                                                                    $ProcedureCount = $objUsafProcedureCategory->GetAllUsafProcedureCountByCategory($procedureCategoryId);
                                                                    // $ProcedureCount = $objUsafProcedureCategory->GetAllUsafProcedureCountByCourse($procedureCategoryId, $courseId);
                                                                } else {
                                                                    $ProcedureCount = $objProcedureCategory->GetAllProcedureCountForPreceptor($procedureCategoryId, $currentSchoolId, 0, $procedurecountIds, $isActiveCheckoff);
                                                                }
                                                            } else {
                                                                if (!$externalPreceptorId) {
                                                                    $ProcedureCount = $objProcedureCategory->GetAllAdvanceProcedureCountByCategory($procedureCategoryId);
                                                                    // $ProcedureCount = $objProcedureCategory->GetAllAdvanceProcedureCountByCourse($procedureCategoryId, $courseId);
                                                                } else {
                                                                    $ProcedureCount = $objProcedureCategory->GetAllProcedureCountForPreceptor($procedureCategoryId, $currentSchoolId, 0, $procedurecountIds, $isActiveCheckoff);
                                                                }
                                                            }

                                                            if ($ProcedureCount != '') {
                                                                $totalProcedureCount = mysqli_num_rows($ProcedureCount);
                                                            }


                                                            if ($totalProcedureCount > 0) {
                                                                while ($row = mysqli_fetch_array($ProcedureCount)) {
                                                                    $procedures[] = $row;
                                                                    $proceduteCountTopicId = $row['proceduteCountId'];
                                                                    $proceduteCountName = $row['proceduteCountName'];

                                                                    $pname = explode("-", $proceduteCountName);

                                                                    $TotalCount = count($pname);
                                                                    $substrs = $pname[0];

                                                                    //print_r($substrs);

                                                                    if ($isActiveCheckoff == 2) {

                                                                        $shortTitlelen = strlen($proceduteCountName);

                                                                        if ($shortTitlelen > 40) {

                                                                            $schoolproceduteCountName = substr($proceduteCountName, 0, 40);
                                                                            $schoolproceduteCountName .= '...';
                                                                        } else {
                                                                            $schoolproceduteCountName = $proceduteCountName;
                                                                        }
                                                                    } else if ($isActiveCheckoff == 1) {
                                                                        if ($TotalCount == 3) {
                                                                            $alllow = (strtolower($substrs)) ? strtolower($substrs) : '';

                                                                            $FirstQuestionName = '';
                                                                            $QuestionName = $pname[0];
                                                                            if (strpos($alllow, 'foundation') != false) {
                                                                                $FirstQuestionName = str_replace('PEF Foundation', ' ', $QuestionName);
                                                                            }
                                                                            if (strpos($alllow, 'activity ') != false) {
                                                                                $FirstQuestionName = str_replace('PEF Activity', ' ', $QuestionName);
                                                                            }
                                                                            $lastname = $pname[2];
                                                                            $Procedurename = $FirstQuestionName . '-' . $lastname;
                                                                        } else {
                                                                            $QuestionName = $pname[0];
                                                                            if (strpos($alllow, 'foundation') != false) {
                                                                                $FirstQuestionName = str_replace('PEF Foundation', ' ', $QuestionName);
                                                                            }
                                                                            if (strpos($alllow, 'activity ') != false) {
                                                                                $FirstQuestionName = str_replace('PEF Activity', ' ', $QuestionName);
                                                                            }
                                                                            $name = isset($pname[1]) ? $pname[1] : '';
                                                                            $lastname = isset($pname[3]) ? $pname[3] : '';
                                                                            $Procedurename = $FirstQuestionName . '-' . $name . '-' . $lastname;
                                                                        }


                                                                        $shortTitlelen = strlen($Procedurename);

                                                                        if ($shortTitlelen > 40) {

                                                                            $schoolproceduteCountName = substr($Procedurename, 0, 40);
                                                                            $schoolproceduteCountName .= '...';
                                                                        } else {
                                                                            $schoolproceduteCountName = $Procedurename;
                                                                        }
                                                                    } else {

                                                                        if ($TotalCount == 2) {
                                                                            $alllow = strtolower($substrs);

                                                                            $FirstQuestionName = '';
                                                                            $QuestionName = $pname[0];

                                                                            $lastname = $pname[1];
                                                                            $Procedurename = $QuestionName . '-' . $lastname;
                                                                        } else if ($TotalCount == 3) {
                                                                            if ($proceduteCountName == 'N-VentInit - Neonate Ventilator Initiation' || $proceduteCountName == 'N-VentMon - Neo/Ped Ventilator Monitoring') {
                                                                                $QuestionName = $pname[0];
                                                                                $QuestionNameSecond = $pname[1];
                                                                                $lastname = $pname[2];
                                                                                $Procedurename = $QuestionName . '-' . $QuestionNameSecond . '-' . $lastname;
                                                                            } else {
                                                                                $QuestionName = $pname[0];
                                                                                $lastname = $pname[2];
                                                                                $Procedurename = $QuestionName . '-' . $lastname;
                                                                            }
                                                                        } else {
                                                                            $QuestionName = $pname[0];
                                                                            if (strpos($alllow, 'foundation') != false) {
                                                                                $FirstQuestionName = str_replace('PEF Foundation', ' ', $QuestionName);
                                                                            }
                                                                            if (strpos($alllow, 'activity ') != false) {
                                                                                $FirstQuestionName = str_replace('PEF Activity', ' ', $QuestionName);
                                                                            }
                                                                            $name = $pname[1];
                                                                            $lastname = $pname[3];
                                                                            $Procedurename = $FirstQuestionName . '-' . $name . '-' . $lastname;
                                                                        }


                                                                        $shortTitlelen = strlen($Procedurename);

                                                                        if ($shortTitlelen > 40) {

                                                                            $schoolproceduteCountName = substr($Procedurename, 0, 40);
                                                                            $schoolproceduteCountName .= '...';
                                                                        } else {
                                                                            $schoolproceduteCountName = $Procedurename;
                                                                        }
                                                                    }

                                                                    $procedureCountsCode = $row['procedureCountsCode'];
                                                                    // $procedureCountsCodeArray = explode(',', $procedureCountsCode);
                                                                    // Add each procedureCountsCode value to the array
                                                                    $procedureCountsCodeArray[] = $row['procedureCountsCode'];
                                                                    $jsonProcedureCountsCodeArray = json_encode($procedureCountsCodeArray);


                                                                    $total = 0;
                                                                    $total = $total ? $total : 0;
                                                                    $evalutionDate = '';
                                                                    $procedurePointsPerformTotal = 0;
                                                                    $procedurePointsPerformTotal = $procedurePointsPerformTotal ? $procedurePointsPerformTotal : 0;

                                                                    if ($currentSchoolId == 127) {
                                                                        $proceduteCountName = $row['proceduteCountName'];
                                                                        $schoolproceduteCountName = $row['proceduteCountName'];
                                                                    }
                                                                    if ($proceduteCountTopicId == 174)
                                                                        $schoolproceduteCountName = 'NRebrMask - Non-Rebreather Mask';



                                                                    //Edit
                                                                    $procedureId = 0;
                                                                    $GetProcedureCount = '';
                                                                    if (!$externalPreceptorId) {
                                                                        $objProcedureCount = new clsProcedureCount();
                                                                        $GetProcedureCount = $objProcedureCount->GetProcedureCountTotalForActivitySheet($studentId, $rotationId, $proceduteCountTopicId, $activityId);
                                                                    }

                                                                    $total = ($externalPreceptorId > 0) ? $row['Total'] : $GetProcedureCount['Total'] ?? 0;

                                                                    $total = $total ? $total : 0;
                                                                    $procedurePointsAssist = ($externalPreceptorId > 0) ? $row['procedurePointsAssist'] : $GetProcedureCount['procedurePointsAssist'] ?? '';
                                                                    $procedurePointsObserve = ($externalPreceptorId > 0) ? $row['procedurePointsObserve'] : $GetProcedureCount['procedurePointsObserve'] ?? '';
                                                                    $procedurePointsPerform = ($externalPreceptorId > 0) ? $row['procedurePointsPerform'] : $GetProcedureCount['procedurePointsPerform'] ?? '';

                                                                    $procedurePointsPerformTotal = ($externalPreceptorId > 0) ? $row['procedurePointsPerformTotal'] : $GetProcedureCount['procedurePointsPerformTotal'] ?? 0;
                                                                    $procedurePointsPerformTotal = $procedurePointsPerformTotal ? $procedurePointsPerformTotal : 0;
                                                                    $evalutionDate = ($externalPreceptorId > 0) ? $row['evaluationDate'] : $GetProcedureCount['evaluationDate'] ?? 0;
                                                                    $procedureDate = ($externalPreceptorId > 0) ? $row['procedureDate'] : $GetProcedureCount['procedureDate'] ?? 0;
                                                                    if ($evalutionDate != '0000-00-00' && $evalutionDate != '' && $evalutionDate != '0000-00-00 00:00:00' && $evalutionDate != '01/01/1970' && $evalutionDate != '12/31/1969') {
                                                                        // $evalutionDate = converFromServerTimeZone($evalutionDate, $TimeZone);
                                                                        $evalutionDate = date("m/d/Y", strtotime($evalutionDate));
                                                                    } else {
                                                                        $evalutionDate = '';
                                                                    }
                                                                    if ($procedureDate != '0000-00-00' && $procedureDate != '' && $procedureDate != '0000-00-00 00:00:00' && $procedureDate != '01/01/1970' && $procedureDate != '12/31/1969') {
                                                                        // $procedureDate = converFromServerTimeZone($procedureDate, $TimeZone);
                                                                        $procedureDate = date("m/d/Y", strtotime($procedureDate));
                                                                    } else {
                                                                        $procedureDate = '';
                                                                    }
                                                                    $MatsreProcedureCountId = ($externalPreceptorId > 0) ? $row['procedureCountId'] : $GetProcedureCount['procedureCountId'] ?? 0;

                                                                    $procedureId = ($MatsreProcedureCountId) ? $MatsreProcedureCountId : 0;

                                                            ?>
                                                                    <!-- <tr>
                                                                        <td><?php echo $procedureCountsCode; ?></td>
                                                                        <?php if ($isActiveCheckoff == 1 || $isActiveCheckoff == 2) { ?>
                                                                            <td class="point" proceduteCountTopicId="<?php echo $proceduteCountTopicId; ?>"><a title="Click To View Procedure Steps" href="showTopicStepsInModel.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>&proceduteCountTopicId=<?php echo EncodeQueryData($proceduteCountTopicId); ?>&procedureCountsCode=<?php echo $procedureCountsCode; ?>" class="procedureTopicSteps"> <?php echo $schoolproceduteCountName; ?></a></td>
                                                                        <?php } else { ?>
                                                                            <td class="point" title="<?php echo $proceduteCountName; ?>" proceduteCountTopicId="<?php echo $proceduteCountTopicId; ?>"><?php echo $schoolproceduteCountName; ?></td>
                                                                        <?php }  ?>
                              x                                          <input type="hidden" id="procedureId-<?php echo $procedureCountsCode; ?>" value="<?php echo $procedureId; ?>">
                                                                        <td><input type="text" data-parsley-type="number" proceduteCountTopicId="<?php echo $proceduteCountTopicId; ?>" class="point form-control input-md asstd" style="text-align:right; width:50px" id="txtAsstd-<?php echo $procedureCountsCode; ?>" value="<?php echo $procedurePointsAssist; ?>"></td>
                                                                        <td><input type="text" data-parsley-type="number" proceduteCountTopicId="<?php echo $proceduteCountTopicId; ?>" class="point form-control input-md obsvd" style="text-align:right; width:50px" id="txtobsvd-<?php echo $procedureCountsCode; ?>" value="<?php echo $procedurePointsObserve; ?>"></td>
                                                                        <td><input type="text" data-parsley-type="number" proceduteCountTopicId="<?php echo $proceduteCountTopicId; ?>" class="point form-control input-md prfmd" style="text-align:right; width:50px" id="txtprfmd-<?php echo $procedureCountsCode; ?>" value="<?php echo $procedurePointsPerform; ?>"></td>
                                                                        <td><span id="prfmTotal-<?php echo $procedureCountsCode; ?>" class="point prfmdtotal editPerformTotal" style="width:30px"><?php echo ($procedurePointsPerformTotal); ?></span></td>
                                                                        <td><span id="procdrTotal-<?php echo $procedureCountsCode; ?>" class="sumTotal"> <?php echo ($total); ?></span></td>
                                                                        <td>
                                                                            <div class="position-relative table-datepicker" style="width:160px">

                                                                                <input type="text" id="procedurecountDate_<?php echo $procedureCountsCode ?>" style="width:160px" proceduteCountTopicId="<?php echo $proceduteCountTopicId; ?>" studentId="<?php echo $studentId; ?>" class=" point  procedureDate form-control input-md dateInputFormat" value="<?php echo $procedureDate; ?>" data-parsley-errors-container="#error-drugScreeningDate" placeholder="MM-DD-YYYY" />
                                                                                <span class="input-group-addon" style="z-index: auto;">
                                                                                    <span class="glyphicon glyphicon-calendar" style="width:85px"></span>
                                                                                </span>
                                                                            </div>
                                                                        </td>
                                                                        <td><?php echo $evalutionDate; ?></td>
                                                                    </tr> -->

                                                                    <div class="col-md-6 card-mobile-padding  mb-15 mt-10">
                                                                        <div class="precedure-count-card calculateTableValues">
                                                                            <div class="procedure-count-card-header">
                                                                                <div class="procedure-name-section">
                                                                                    <p class="mb-0 name-heading">Procedure Name</p>
                                                                                    <p class="procedure-count-name"> <a href="javascript:void(0)" class="getProcedureSteps" rotationId="<?php echo EncodeQueryData($rotationId) ?>" proceduteCountTopicId="<?php echo EncodeQueryData($proceduteCountTopicId); ?>" procedureCountsCode="<?php echo $procedureCountsCode; ?>"> <?php echo $schoolproceduteCountName; ?></a></p>
                                                                                </div>
                                                                                <div style="text-align: end;">
                                                                                    <p class="mb-0">ID</p>
                                                                                    <p class="procedure-count-name"><?php echo $procedureCountsCode; ?> </p>
                                                                                </div>
                                                                            </div>
                                                                            <div class="row mb-5 procedeure-count-form-field-main">
                                                                                <input type="hidden" id="procedureId-<?php echo $procedureCountsCode; ?>" value="<?php echo $procedureId; ?>">
                                                                                <div class="col-md-6 procedure-count-form-field">
                                                                                    <p class="mb-5">Asstd</p>
                                                                                    <input type="text" data-parsley-type="number" proceduteCountTopicId="<?php echo $proceduteCountTopicId; ?>" class="point form-control input-md asstd" id="txtAsstd-<?php echo $procedureCountsCode; ?>" value="<?php echo $procedurePointsAssist; ?>">
                                                                                </div>
                                                                                <div class="col-md-6 procedure-count-form-field">
                                                                                    <p class="mb-5">Obsvd</p>
                                                                                    <input type="text" data-parsley-type="number" proceduteCountTopicId="<?php echo $proceduteCountTopicId; ?>" class="point form-control input-md obsvd" id="txtobsvd-<?php echo $procedureCountsCode; ?>" value="<?php echo $procedurePointsObserve; ?>">
                                                                                </div>
                                                                            </div>
                                                                            <div class="row procedeure-count-form-field-main">

                                                                                <div class="col-md-6 procedure-count-form-field">
                                                                                    <p class="mb-5">Prfmd</p>
                                                                                    <input type="text" data-parsley-type="number" proceduteCountTopicId="<?php echo $proceduteCountTopicId; ?>" class="point form-control input-md prfmd" id="txtprfmd-<?php echo $procedureCountsCode; ?>" value="<?php echo $procedurePointsPerform; ?>">
                                                                                </div>

                                                                                <div class="col-md-6 procedure-count-form-field">
                                                                                    <p class="mb-5">Total</p>
                                                                                    <span id="procdrTotal-<?php echo $procedureCountsCode; ?>" class="point form-control input-md sumTotal"> <?php echo ($total); ?></span>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>



                                                                    <!-- <p class="text-right w-full">
                                                                        <a href="javascript:void(0)" id="toggle-link">Show All</a>
                                                                    </p> -->

                                                            <?php
                                                                }
                                                            }
                                                            ?>
                                                        </div>



                                                        <div id="more-cards" class="hidden-cards"></div>
                                                    </div>
                                                    <!-- </tbody>

                                                    </table> -->


                                                    <?php
                                                    if ($categorywiseProcedureCount == 0 && $preceptorNum != '') {
                                                        echo 'No Procedure Counts Available';
                                                    }
                                                    ?>

                                                </div>

                                            </div>
                                        </div>
                                    <?php
                                    }


                                    unset($objProcedureCategory);
                                    unset($objcheckoff);
                                    unset($objUsafProcedureCategory);
                                    ?>


                                </div>


                            </div>

                        </div>
                    </div>
                </div>

                <div class="mt-3 flex-end">
                    <button class="button btn-navigate-form-step button-prev" type="button" step_number="2">Prev</button>
                    <button class="button next btn-navigate-form-step" id="btnProcedure" type="button" step_number="4">Next</button>
                </div>

            </section>

            <!-- Step 4 Comments Section -->
            <section id="step-4" class="form-step d-none pt-0" data-parsley-validate>

                <div class="row">
                    <div class="col-md-6">

                        <div class="form-group">
                            <label class="col-md-12 control-label" for="ClinicianDate">Date of Preceptor/Clinician Signature</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <div class='input-group date w-full' id='ClinicianDate1' style="position: relative;">

                                    <input type='text' name="ClinicianDate" id="ClinicianDate" class="form-control input-md required-input dateInputFormat" placeholder="MM/DD/YYYY" value="<?php echo ($clinicianSignatureDate); ?>" />
                                    <span class="input-group-addon calender-icon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>

                                </div>
                                <div></div>
                            </div>

                        </div>
                    </div>
                    <div class="col-md-6">

                        <div class="form-group">
                            <label class="col-md-12 control-label" for="SchoolDate">Date of School Signature</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <div class='input-group date w-full' id='SchoolDate1' style="position: relative;">

                                    <input type='text' name="SchoolDate" id="SchoolDate" class="form-control input-md dateInputFormat" placeholder="MM/DD/YYYY" value="<?php echo ($schoolSignatureDate); ?>" readonly />
                                    <span class="input-group-addon calender-icon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>


                            </div>
                        </div>

                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 ">
                        <div class="form-group m-0">
                            <label class="control-label" for="">Preceptor/Clinician Comment </label>
                        </div>
                    </div>
                    <div class="col-md-12 text-left" style="margin-top: 5px;">
                        <div class="form-group">
                            <div class="col-md-12">
                                <textarea name="clinician_comments" rows="5" id="clinician_comments" class="form-control"><?php echo ($clinicianComment); ?></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group m-0">
                            <label class="control-label" for="">School Comment </label>
                        </div>
                    </div>
                    <div class="col-md-12 text-left" style="margin-top: 5px;">
                        <div class="form-group">
                            <div class="col-md-12">
                                <textarea name="school_comments" rows="5" id="school_comments" class="form-control" readonly><?php echo ($schoolComment); ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3 flex-end">
                    <button class="button btn-navigate-form-step button-prev" type="button" step_number="3">Prev</button>
                    <?php if ($view == 1) { ?>
                        <?php if ($IsMobile) { ?>
                            <a type="button" href="<?php echo BASE_PATH ?>/webRedirect.html?status=Cancel&type=activitysheet" class="btn btn-default">Cancel</a>
                        <?php } else { ?>
                            <a href="activitysheetlist.html" class="btn btn-default">Cancel</a>

                        <?php }
                    } else {
                        if ($externalPreceptorId) { ?>
                            <button id="btnSubmit" name="btnSubmit" class="btn btn-success mr-15">Signoff</button>
                        <?php } else { ?>
                            <button id="btnSubmit" name="btnSubmit" class="btn btn-success mr-15">Save</button>
                    <?php }
                    } ?>
                </div>
            </section>

        </form>

    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <?php include('../includes/procedureStepsModal.php'); ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/inputmask/jquery.inputmask.bundle.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/inputmask/inputmask.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/common.js"></script>
    <!-- Auto Save Script Start -->
    <script>
        var rotationId = '<?php echo $rotationId; ?>';

        <?php if ($isAutoSave && $activityId == 0) { ?>

            // --- Auto-save setup using common.js ---
            function startAutoSaveIfNeeded() {
                if (!window.autosaveStarted) {
                    window.autosaveStarted = true;
                    setInterval(function() {
                        $('#formActivitySheet input, #formActivitySheet select, #formActivitySheet textarea').prop('disabled', false);
                        updateDataArray();
                        autoSaveDraft({
                            formSelector: '#formActivitySheet',
                            saveUrl: '../../../ajax/ajax_autosave.html',
                            draftsListCallback: loadDrafts,
                            draftIdField: '#draftId',
                            hiddenDraftIdField: '#hiddendraftId',
                            statusSelector: '#status',
                            moduleName: 'activitysheet'
                        });                        
                    }, 60000);
                }
            }
            document.addEventListener("DOMContentLoaded", function () {
                var form = document.getElementById('formActivitySheet');
                if (form) {
                    form.addEventListener('input', startAutoSaveIfNeeded, {
                        once: true
                    });
                }
            });
            $(document).ready(loadDrafts);

            function loadDataIntoForm(currentSchoolId, userId, moduleName, file) {
                $.ajax({
                    url: '../../../upload/schools/' + currentSchoolId + '/student/' + userId + '/autosave/' + moduleName + '/' + file,
                    method: 'GET',
                    dataType: 'json',
                    success: function (data) {
                        console.log(data);
                        if (!data) return;
                        // Store draft data in localStorage
                        localStorage.setItem('draftFormData', JSON.stringify(data));
                        // Bind immediately after storing
                        bindDraftDataFromLocalStorage();
                    },
                    error: function (xhr, status, error) {
                        console.error('Failed to load draft data:', error, xhr.responseText);
                        alert('Failed to load draft data. Please check the console for details.');
                    }
                });
            }

            // Function to disable all next buttons
            function disableNextButtons() {
                $('#btnJournal').prop('disabled', true);
                $('#btnProcedure').prop('disabled', true);
                $('.btn-navigate-form-step.next').prop('disabled', true);
            }

            // Function to enable all next buttons
            function enableNextButtons() {
                $('#btnJournal').prop('disabled', false);
                $('#btnProcedure').prop('disabled', false);
                $('.btn-navigate-form-step.next').prop('disabled', false);
            }

            // Function to bind draft data from localStorage to form elements
            function bindDraftDataFromLocalStorage() {


                var data = localStorage.getItem('draftFormData');
                if (!data) {
                    // No data available - disable all next buttons
                    disableNextButtons();
                    return;
                }
                data = JSON.parse(data);
                var draftClinicianValue = null;
                Object.keys(data).forEach(function (key) {
                
                    var value = data[key];
                    var $select2 = $('select[name="' + key + '"], select#' + key + '.select2_single');
                    if ($select2.length) {
                        if (key === 'cborotation') {
                            // Set cborotation only once if rotationId == 0
                            var rotationId = $('#cborotation').val();
                            if (rotationId == 0) {
                                $select2.val(value).trigger('change');
                            }
                        } else {
                            // For other select2 fields, set only if rotationId > 0
                            var rotationId = $('#cborotation').val();
                            if (rotationId > 0) {
                                $select2.val(value).trigger('change');
                            }
                        }
                        if (key === 'cboClinicalinstructor' || key === 'cboClinicalinstructor[]') {
                            draftClinicianValue = value;
                        }
                        return;
                    }
                    if (key === 'cboClinicalinstructor' || key === 'cboClinicalinstructor[]') {
                        draftClinicianValue = value;
                        return;
                    }
                    // Bind draft value to checkbox element (by id or name)
                    var $checkboxById = $('input[type="checkbox"]#' + key);
                    var $checkboxByName = $('input[type="checkbox"][name="' + key + '"]');
                    var $checkbox = $checkboxById.length ? $checkboxById : $checkboxByName;

                    if ($checkbox.length) {
                        var isChecked = value > 0 && (value == 1 || value === true || value === 'true');
                        $checkbox.prop('checked', isChecked);

                        // Manage div visibility and form validation based on checkbox state
                        if (isChecked) {
                            $('#clinician').hide();
                            $('#preceptor-mobile-number').addClass('show').show();
                            $('#preceptorNo').attr('required', true).addClass('required-input');
                        } else {
                            $('#clinician').show();
                            $('#preceptor-mobile-number').removeClass('show').hide();
                            $('#preceptorNo').removeAttr('required').removeClass('required-input');
                        }
                        return;
                    }
                    var $elById = $('[id="' + key + '"]');
                    if ($elById.length) {
                        if ($elById.is('input, textarea, select')) {
                            $elById.val(value);
                        } else if ($elById.is('span')) {
                            $elById.text(value);
                        } else {
                            $elById.html(value);
                        }
                    } else {
                        var $elByName = $('[name="' + key + '"]');
                        if ($elByName.length) {
                            $elByName.val(value);
                            if (key === 'cboClinicalinstructor' || key === 'cboClinicalinstructor[]') {
                                draftClinicianValue = value;
                            }
                        }
                    }
                });
                console.log("draftClinicianValue ",draftClinicianValue);
                               // If draftClinicianValue is set, bind after AJAX loads options
                if (draftClinicianValue !== null) {
                    var trySetClinician = function(attempts) {
                        attempts = attempts || 0;
                        var $cbo = $('#cboClinicalinstructor');
                        // Try to set value only if the option exists
                        if ($cbo.find('option[value="' + draftClinicianValue + '"]').length > 0) {
                            $cbo.val(draftClinicianValue).trigger('change');
                        } else if (attempts < 20) {
                            // Try again after a short delay, up to 2 seconds
                            setTimeout(function() { trySetClinician(attempts + 1); }, 100);
                        } else {
                            // Fallback: set value anyway and trigger change (may not show in UI if option missing)
                            $cbo.val(draftClinicianValue).trigger('change');
                        }
                    };
                    trySetClinician();
                }

                // Enable next buttons since data was successfully loaded
                enableNextButtons();
            }

           
        <?php } ?>
    </script>
    <!-- Auto Save Script End -->

    <script>
        // Show the loading div when the script runs
        // $('#loading-div-background').show();

        // // Hide the loading div when the window is fully loaded
        // $(window).on('load', function() {
        //     // Hide the loading indicator
        //     $('#loading-div-background').hide();
        // });


        $(document).ready(function() {
            $('#toggle-link').on('click', function(e) {
                e.preventDefault();

                var $this = $(this);
                var $moreCards = $('#more-cards');


            });
        });

        $(window).load(function() {

            var hiddendraftId = $('#hiddendraftId').val();
            $('#formActivitySheet')
                .parsley()
                .on('field:validated', function() {
                    var ok = $('.parsley-error').length === 0;
                })
                .on('form:submit', function() {

                    // Call the updateDataArray function
                    updateDataArray();

                    ShowProgressAnimation();

                    // if (!preceptorId) {

                    $('#formActivitySheet input, #formActivitySheet select, #formActivitySheet textarea').prop('disabled', false);
                    // }

                    return true; // Don't submit form for this demo
                });
            $(".select2_single").select2();
            $('#select2-cborotation-container').addClass('required-select2');
            $('#select2-cbohospitalsiteunits-container').addClass('required-select2');
            // $('#select2-cborotation-container').addClass('required-select2');
            $('#select2-cbodrhospitalsiteunits-container').addClass('required-select2');

            $('#btnJournal').prop('disabled', true);
            $('#btnInteraction').prop('disabled', true);

            var journalCount = "<?php echo $journalCount; ?>";
            // console.log(journalCount);
            var currentJouranlLength = $('#student_journal_entry').val().length;
            $('#studentJournal-current').text(currentJouranlLength);

            if (journalCount == 0) {
                $('#btnJournal').prop('disabled', false);
            }

            if (currentJouranlLength > journalCount) {
                $('#btnJournal').prop('disabled', false);
            }

            var interactionCount = "<?php echo $interactionCount; ?>";
            var currentInteractionLength = $('#Student_Response').val().length;
            $('#Student_Response-current').text(currentInteractionLength);


            if (currentInteractionLength > interactionCount) {
                $('#btnInteraction').prop('disabled', false);
            }

            if (interactionCount == 0)
                $('#btnInteraction').prop('disabled', false);

            var activityId = "<?php echo $activityId; ?>";

            var activitydate = '<?php echo $activityDate; ?>';
            if (activitydate) {

            }
            $('#activitysheetdate').datetimepicker({
                format: 'MM/DD/YYYY',
                maxDate: activityId > 0 ? false : moment() // maxDate is applied only if activityId <= 0
            });

            $('.procedureDate').datetimepicker({
                format: 'MM/DD/YYYY',
                maxDate: 'now'

            });
            $('#ClinicianDate').datetimepicker({
                format: 'MM/DD/YYYY',
                maxDate: 'now'
            });

            $('#SchoolDate').datetimepicker({
                format: 'MM/DD/YYYY',
                maxDate: 'now'
            });

            var activityId = '<?php echo $activityId; ?>';
            var rotationId = $('#cborotation').val();
            console.log(rotationId);
            // if (activityId > 0) {

            getHospitalSite(rotationId);
            getclinician(rotationId);
            // }


            $("#interactionType").change(function() {
                var selectedValue = $(this).val();
                var result = $(this).val().split('-')[1];

                $("#txtPointsAwarded").val($.trim(result));
            });
            if(rotationId > 0) {
                // localStorage.removeItem('draftFormData');
                bindDraftDataFromLocalStorage();
                localStorage.removeItem('draftFormData');
            }
            else{

                localStorage.removeItem('draftFormData');
            }
        });

        <?php if ($status == "Added") { ?>
            alertify.success('Activity Sheet added successfully');
            setTimeout(function() {
                window.location.href = "activitySheet.html";
            }, 3000); // Delay of 3000 milliseconds (3 seconds)
        <?php  } ?>


        function getclinician(val) {
            var schoolId = '<?php echo $currentSchoolId; ?>';
            var clinicianId = '<?php echo $clinicianId; ?>';

            $.ajax({
                type: "POST",
                url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_get_clinical_instructor_by_rotation.html",
                data: 'Rotation_Id=' + val + "&schoolId=" + schoolId + "&Clinician_Id=" + clinicianId,
                success: function(data) {
                    $("#cboClinicalinstructor").html(data);

                }
            });
        }

        function getHospitalSite(val) {

            var rotationId = val;
            var studentId = '<?php echo $studentId; ?>';

            //get hospital site from rotation 
            $.ajax({
                type: "POST",
                url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_get_hospitalsites_by_rotation.html",
                data: {
                    rotationId: rotationId,
                    studentId: studentId
                },
                success: function(responseData) {
                    $('#HospitalSite').val(responseData);
                }
            });

        }

        function updateDataArray() {
            const procedureCountsCodes = <?php echo $jsonProcedureCountsCodeArray; ?>;
            const form = document.getElementById('formActivitySheet');


            if (Array.isArray(procedureCountsCodes)) {
                const dataArray = procedureCountsCodes.reduce((acc, code) => {
                    const txtAsstdElement = document.getElementById(`txtAsstd-${code}`);
                    const txtobsvdElement = document.getElementById(`txtobsvd-${code}`);
                    const txtprfmdElement = document.getElementById(`txtprfmd-${code}`);
                    const dateElement = document.getElementById(`procedurecountDate_${code}`);
                    // var currentdate = new Date();
                    // const formattedDate = currentdate.toISOString().split('T')[0];
                    const prfmTotalElement = document.getElementById(`prfmTotal-${code}`);
                    const procdrTotalElement = document.getElementById(`procdrTotal-${code}`);
                    const procedureId = document.getElementById(`procedureId-${code}`);

                    if (txtAsstdElement?.value.trim() || txtobsvdElement?.value.trim() || txtprfmdElement?.value.trim()) {
                        const entry = {
                            txtAsstd: txtAsstdElement.value,
                            txtobsvd: txtobsvdElement.value,
                            txtprfmd: txtprfmdElement.value,
                            // date: formattedDate,
                            prfmTotal: prfmTotalElement?.textContent || '0',
                            procdrTotal: procdrTotalElement?.textContent.trim() || '0',
                            procedureId: procedureId?.value
                        };

                        acc.push({
                            [code]: entry
                        });

                        // Create and append the hidden input for each detail
                        const hiddenField = document.createElement('input');
                        hiddenField.type = 'hidden';
                        hiddenField.name = `procedurecodes[${code}]`;
                        hiddenField.value = JSON.stringify(entry);
                        form.appendChild(hiddenField);
                    }

                    return acc;
                }, []);

                // Serialize and set the dataArray as a JSON string
                const serializedData = JSON.stringify(dataArray);
                // console.log('Serialized Data:', serializedData);

                const hiddenField = document.getElementById('procedurecodes');
                if (hiddenField) hiddenField.value = serializedData;
            }
        }

        var externalPreceptorFirstName = '<?php echo $externalPreceptorFirstName; ?>';
        var preceptorNum = '<?php echo $preceptorNum; ?>';

        if (externalPreceptorFirstName == '' && preceptorNum != '') {
            // alert('hi');
            $('#isReloadPage').click(function() {
                location.reload();
            });
            $('.addCommentpopup').magnificPopup({
                'type': 'ajax',
                'closeOnBgClick': false
            });
            $('#registerPage')[0].click();

        }

        $(document).ready(function() {
            var isActiveCheckoff = '<?php echo $isActiveCheckoff; ?>';
            var externalPreceptorFirstName = '<?php echo $externalPreceptorFirstName; ?>';
            var preceptorNum = '<?php echo $preceptorNum; ?>';
            var isRegister = '<?php echo $isRegister; ?>';
            var externalPreceptorId = '<?php echo $externalPreceptorId; ?>';
            var activityId = '<?php echo $activityId; ?>';
            var preceptorId = '<?php echo $preceptorId; ?>';

            if (preceptorId > 0)
                $("#mode").prop("checked", true).trigger("change");


            if (activityId > 0) {
                //Edit 

                if (externalPreceptorId > 0) {
                    //Preceptor 
                    $("#mode").prop("checked", true).trigger("change");
                    $("#mode").attr("disabled", true);

                    $("#cborotation").attr("disabled", true);
                    $("#activitysheetdate").attr("disabled", true);
                    $("#cbohospitalsiteunits").attr("disabled", true);

                    $("#student_journal_entry").attr("disabled", true);
                    $("#cbodrhospitalsiteunits").attr("disabled", true);
                    $("#txtTimeSpent").attr("disabled", true);
                    $("#Student_Response").attr("disabled", true);
                    $(".asstd").attr("disabled", true);
                    $(".obsvd").attr("disabled", true);
                    $(".prfmd").attr("disabled", true);
                    $(".sumTotal").attr("disabled", true);

                    $("#ClinicianDate").attr("disabled", false);
                    $("#ClinicianDate").attr("required", true);
                    $("#procedureDate").attr("disabled", true);
                    $("#clinician_comments").attr("disabled", false);
                    $("#cbophysician").attr("disabled", true);

                    if (isActiveCheckoff == 2) {

                        $("#txtPointsAwarded").attr("disabled", true);
                        $("#interactionType").attr("disabled", true);
                    } else {
                        $("#txtPointsAwarded").attr("disabled", false);
                    }
                } else {

                    $("#mode").attr("disabled", true);
                    $("#cborotation").attr("disabled", true);
                    $("#activitysheetdate").attr("disabled", true);
                    $("#ClinicianDate").attr("disabled", true);
                    $("#ClinicianDate").attr("required", false);
                    $("#clinician_comments").attr("disabled", true);

                }

                $("#preceptorName").attr("disabled", true);
                $('#select2-cboClinicalinstructor-container').removeClass('required-select2');
                $('#cboClinicalinstructor').attr('required', false);
                $('#cboClinicalinstructor').attr('disabled', true);

            } else {
                //Add 

                $("#ClinicianDate").attr("disabled", true);
                $("#clinician_comments").attr("disabled", true);
            }


            function changeMode(isPreceptor) {
                if (isPreceptor) {
                    // Preceptor state
                    // console.log("Preceptor");
                    $('#clinician').hide();
                    $('#preceptor-mobile-number').addClass('show');
                    $('#preceptor-mobile-number').show();
                    $('#preceptorNo').attr('required', true);
                    $('#preceptorNo').addClass('required-input');
                    $('#select2-cboClinicalinstructor-container').removeClass('required-select2');
                    $('#cboClinicalinstructor').prop('disabled', true);
                    $('#cboClinicalinstructor').attr('required', false);
                    $('#cboClinicalinstructor').val('');
                    var rotationId = '<?php echo $rotationId; ?>';
                    getclinician(rotationId);

                    $('#btnSubmit').on('click', function() {

                        if ($('#preceptor-mobile-number').hasClass('show')) {
                            var value = $('#preceptorNo').val();
                            var rsmobileNo = value.replace(/[_-]/g, '');
                            var mobileNoLength = rsmobileNo.length;

                            if (mobileNoLength != 10) {
                                alertify.error('Invalid Mobile Number');
                                return false; // Prevent form submission
                            }
                        }
                    });

                } else {
                    // Clinician state
                    // console.log("Clinician");

                    $('#clinician').show();
                    $('#preceptor-mobile-number').removeClass('show');
                    $('#preceptor-mobile-number').hide();
                    $('#preceptorNo').removeAttr('required');
                    $('#preceptorNo').removeClass('required-input');
                    $('#preceptorNo').val('');
                    $('#select2-cboClinicalinstructor-container').addClass('required-select2');
                    if (activityId == 0) {
                        $('#cboClinicalinstructor').attr('required', true);
                        $('#cboClinicalinstructor').attr('disabled', false);
                    }
                }
            }



            // Call the function to set initial state
            changeMode($('#mode').is(':checked'));

            // Attach click event to the checkbox
            $('#mode').click(function() {
                changeMode($(this).is(':checked'));
            });

            // Initialize Inputmask
            $('#preceptorNo').inputmask('************');

            // Custom validation
            $('#preceptorNo').on('input', function() {

                var value = $(this).val();
                var rsmobileNo = value.replace(/[_-]/g, '');
                var mobileNoLength = rsmobileNo.length;

                if (mobileNoLength != 10) {
                    $('#txtpreceptorNo').text('Invalid Preceptor Phone Number');
                } else {
                    $('#txtpreceptorNo').text('');
                }
            });

            $('#cborotation').on('change', function() {

                var rotationId = $(this).val();
                var IsMobile = '<?php echo $IsMobile; ?>';

                // getHospitalSite(rotationId);
                // getclinician(rotationId);

                console.log(IsMobile);
                console.log(rotationId);
                if (IsMobile) {
                    window.location.href = "activitySheet.html?rotationId=" + btoa(rotationId) + "&IsMobile=" + (IsMobile);

                } else {

                    window.location.href = "activitySheet.html?rotationId=" + btoa(rotationId);
                }

            });


            // For Interaction
            // character counter and save button enable/disable 
            $('#Student_Response').on('input', function() {
                var currentLength = $(this).val().length;
                $('#Student_Response-current').text(currentLength);

                var interactionCount = "<?php echo $interactionCount; ?>";

                if (interactionCount > 0) {
                    if (currentLength > interactionCount) {
                        $('#btnInteraction').prop('disabled', false);
                    } else {
                        $('#btnInteraction').prop('disabled', true);
                    }
                } else {
                    if (currentLength === 0 || currentLength > interactionCount) {
                        $('#btnInteraction').prop('disabled', false);
                    } else {
                        $('#btnInteraction').prop('disabled', true);
                    }
                }

            });


            //character counter and save button enable/disable
            $('#student_journal_entry').on('input', function() {
                var currentLength = $(this).val().length;
                $('#studentJournal-current').text(currentLength);

                var journalCount = "<?php echo $journalCount; ?>";

                if (journalCount > 0) {
                    if (currentLength > journalCount) {
                        $('#btnJournal').prop('disabled', false);
                    } else {
                        $('#btnJournal').prop('disabled', true);
                    }
                } else {
                    if (currentLength === 0 || currentLength > journalCount) {
                        $('#btnJournal').prop('disabled', false);
                    } else {
                        $('#btnJournal').prop('disabled', true);
                    }
                }

            });


            $('#btnJournal').on('click', function() {

                var rotationId = $('#cborotation').val();
                var activitysheetdate = $('#activitysheetdate').val();
                // console.log(rotationId);

                if (rotationId && activitysheetdate) {

                    $('#cborotation').prop('disabled', true);
                    $('#activitysheetdate').prop('disabled', true);
                }

                $('#mode').prop('disabled', true);

            });

            //Procedure count 
            // $(".calculateTableValues").on("keyup", ".point", function() {

            //     var asstd = $(this).closest("tr").find(".asstd").val();
            //     asstd = parseInt(asstd);

            //     var obsvd = $(this).closest("tr").find(".obsvd").val();
            //     obsvd = parseInt(obsvd);

            //     var prfmd = $(this).closest("tr").find(".prfmd").val();
            //     prfmd = parseInt(prfmd);

            //     if (prfmd > 0) {
            //         $(this).closest("tr").find(".prfmdtotal").text(prfmd);
            //     }

            //     var sumTotal = asstd + obsvd + prfmd;
            //     console.log(sumTotal);

            //     if (sumTotal > 0) {
            //         $(this).closest("tr").find(".sumTotal").text(sumTotal);
            //     }
            //     console.log("Asstd:", asstd, "Obsvd:", obsvd, "Prfmd:", prfmd, "Sum Total:", sumTotal);

            // });
            $(".calculateTableValues").on("keyup", ".point", function() {
                var container = $(this).closest(".precedure-count-card");

                var asstd = parseInt(container.find(".asstd").val()) || 0;
                var obsvd = parseInt(container.find(".obsvd").val()) || 0;
                var prfmd = parseInt(container.find(".prfmd").val()) || 0;

                var sumTotal = asstd + obsvd + prfmd;

                container.find(".sumTotal").text(sumTotal);
            });


            var view = '<?php echo $view; ?>';
            if (view == 1) {
                $('#formActivitySheet input, #formActivitySheet select, #formActivitySheet textarea').prop('disabled', true);
                $(".sumTotal").attr("disabled", true);
            }
        });


        $('.next').on('click', function(event) {
            event.preventDefault();
            var preceptorId = '<?php echo $preceptorId; ?>';
            // console.log(preceptorId);
            var isValid = $('#step-1').parsley().validate();
            $('#cborotation').parsley().validate();
            $('#activitysheetdate').parsley().validate();
            if (preceptorId == 0) {

                $('#preceptorNo').parsley().validate();
                $('#cboClinicalinstructor').parsley().validate();
            }


            if (!isValid) {
                return false;
            }
        });
        /**
         * Define a function to navigate betweens form steps.
         * It accepts one parameter. That is - step number.
         */
        const navigateToFormStep = (stepNumber) => {
            /* Hide all form steps.*/
            document.querySelectorAll(".form-step").forEach((formStepElement) => {
                formStepElement.classList.add("d-none");
            });
            /* Mark all form steps as unfinished.*/
            document.querySelectorAll(".form-stepper-list").forEach((formStepHeader) => {
                formStepHeader.classList.add("form-stepper-unfinished");
                formStepHeader.classList.remove("form-stepper-active", "form-stepper-completed");
            });
            /* Show the current form step (as passed to the function).*/
            document.querySelector("#step-" + stepNumber).classList.remove("d-none");
            /* Select the form step circle (progress bar).*/
            const formStepCircle = document.querySelector('li[step="' + stepNumber + '"]');
            /* Mark the current form step as active.*/
            formStepCircle.classList.remove("form-stepper-unfinished", "form-stepper-completed");
            formStepCircle.classList.add("form-stepper-active");
            /**
             * Loop through each form step circles.
             * This loop will continue up to the current step number.
             * Example: If the current step is 3,
             * then the loop will perform operations for step 1 and 2.
             */
            for (let index = 0; index < stepNumber; index++) {
                /* Select the form step circle (progress bar).
                 */
                const formStepCircle = document.querySelector('li[step="' + index + '"]');
                /*Check if the element exist. If yes, then proceed.
                 */
                if (formStepCircle) {
                    /* Mark the form step as completed.
                     */
                    formStepCircle.classList.remove("form-stepper-unfinished", "form-stepper-active");
                    formStepCircle.classList.add("form-stepper-completed");
                }
            }
        };


        document.querySelectorAll(".btn-navigate-form-step").forEach((formNavigationBtn) => {
            formNavigationBtn.addEventListener("click", () => {
                const stepNumber = parseInt(formNavigationBtn.getAttribute("step_number"));

                if (stepNumber === 2) { // Check if navigating to the second step
                    const isValidStep1 = $('#step-1').parsley().validate(); // Trigger validation for step 1

                    if (!isValidStep1) {
                        return false;
                    }
                }

                if (stepNumber === 3) { // Check if navigating to the second step
                    const isValidStep2 = $('#step-2').parsley().validate(); // Trigger validation for step 1

                    if (!isValidStep2) {
                        return false;
                    }
                }

                if (stepNumber === 4) { // Check if navigating to the second step
                    const isValidStep3 = $('#step-3').parsley().validate(); // Trigger validation for step 1

                    if (!isValidStep3) {
                        return false;
                    }
                }
                // Proceed to navigate to the desired step if validation passes or for other steps
                navigateToFormStep(stepNumber);
            });
        });

        $('.form-stepper-list').click(function() {
            var view = '<?php echo $view; ?>';
            var activityId = '<?php echo $activityId; ?>';

            stepId = $(this).attr('step');
            stepNextId = $('.form-stepper-active').attr('step');
            if (preceptorNum != '' && activityId > 0) {
                navigateToFormStep(stepId);
            } else {
                if (stepId < stepNextId)
                    navigateToFormStep(stepId);
            }

        });

        // Collapsible start
        // Select all collapsibles
        const collapsibles = document.querySelectorAll('.collapsible');

        // Add click event listeners to each collapsible
        collapsibles.forEach(collapsible => {
            collapsible.addEventListener('click', (event) => {
                // Select the content panel following the collapsible link
                const content = collapsible.nextElementSibling;
                const icon = collapsible.querySelector('i.fa-angle-down');

                // Check if the content is currently active (visible)
                const isActive = content.classList.contains('active');

                // Close all content sections and reset arrows
                document.querySelectorAll('.panel-collapse.collapse').forEach(item => {
                    item.classList.remove('active');
                    item.style.maxHeight = null;
                });

                document.querySelectorAll('i.fa-angle-down').forEach(item => {
                    item.classList.remove('rotate');
                });

                // Toggle the content's active state and arrow rotation if it's not active
                if (!isActive) {
                    content.classList.add('active');
                    icon.classList.add('rotate');
                    content.style.maxHeight = content.scrollHeight + 'px'; // Transition height
                }

                // Stop the click event from propagating to the document
                event.stopPropagation();
            });
        });

        // Add a click event listener to the document to detect clicks outside the collapsibles
        // document.addEventListener('click', (event) => {
        //     // Check if the click is outside any collapsible or its content
        //     let isClickInside = false;
        //     collapsibles.forEach(collapsible => {
        //         const content = collapsible.nextElementSibling;
        //         if (collapsible.contains(event.target) || content.contains(event.target)) {
        //             isClickInside = true;
        //         }
        //     });

        //     // If the click is outside, close all open contents
        //     if (!isClickInside) {
        //         document.querySelectorAll('.panel-collapse.collapse').forEach(item => {
        //             item.classList.remove('active');
        //             item.style.maxHeight = null;
        //         });

        //         document.querySelectorAll('i.fa-angle-down').forEach(item => {
        //             item.classList.remove('rotate');
        //         });
        //     }
        // });
        // Collapsible end


        $('.showMore_6_10').click(function() {
            var islast = $(this).attr('islast');
            var listId = parseInt(islast) + 1;
            $(this).attr('islast', listId);
            var divHtml = '<div class="col-md-12 p-0" id="row_' + listId + '" isrow="' + listId + '"> <div class="col-md-6"> <div class="form-group"> <div class="col-md-12    col-sm-12 col-xs-12 mobile-block"><label class="control-label labelcount margin-right" for="adultMedicationsUse' + listId + '">' + listId + '</label>  <input type="text" class="form-control input-md" value="" name="adultMedicationsUseList[' + listId + '][]" id="adultMedicationsUse' + listId + '" placeholder="Enter Medications w/Indications "> </div> </div> </div> <div class="col-md-6"> <div class="form-group" style="display: flex;align-items: center;"> <div class="col-md-11 col-sm-12 col-xs-12"> <textarea type="text" class="form-control input-md" rows="2" cols="5" name="adultModificationCarePlanList[' + listId + '][]" id="adultModificationCarePlan' + listId + '" placeholder="Enter Modifications to Care Plan "></textarea> </div> <div> <a  href="javascript:void(0);" class="deleteRow row-delete-icon" style="padding-left:0;" deleteRowId="' + listId + '" ><i class="fa fa-trash-o" aria-hidden="true" style="font-size: 18px;"></i></a> </div> </div> </div> </div>';
            $('#MedicationDiv').append(divHtml);
        });
        $(document).on('click', '.deleteRow', function() {
            var lastrowId = $('#MedicationDiv').children().last().attr('isrow');
            var deleteRowId = $(this).attr('deleteRowId');
            $('#row_' + deleteRowId).remove();
            if (deleteRowId == lastrowId)
                var lastrowId = parseInt(lastrowId) - 1;

            $('.showMore_6_10').attr('islast', lastrowId);
            var islast = $('.showMore_6_10').attr('islast');
        });
    </script>
</body>

</html>